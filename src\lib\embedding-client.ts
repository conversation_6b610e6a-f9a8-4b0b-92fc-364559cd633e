'use client';

import { APP_CONFIG } from '@/lib/config';

// Primary embedding function with fallback support
export async function generateQAEmbedding(question: string, answer: string = ''): Promise<number[]> {
  const embeddingConfig = APP_CONFIG.api.embedding;
  console.log(`🌐 Generating ${embeddingConfig.provider} embedding...`);

  try {
    return await generatePrimaryEmbedding(question, answer);
  } catch (error) {
    console.warn('⚠️ Primary embedding failed, falling back to browser-based embedding:', error);

    // Fallback to browser-based embedding
    try {
      console.log('🧠 Attempting browser-based embedding as fallback...');
      return await generateClientSideEmbedding(question, answer);
    } catch (fallbackError) {
      console.error('❌ All embedding methods failed:', fallbackError);
      throw new Error('Failed to generate embedding: All methods failed');
    }
  }
}

// Specialized function for search queries (only question, no answer)
export async function generateSearchEmbedding(query: string): Promise<number[]> {
  const embeddingConfig = APP_CONFIG.api.embedding;
  console.log(`🌐 Generating ${embeddingConfig.provider} search embedding...`);

  try {
    return await generatePrimarySearchEmbedding(query);
  } catch (error) {
    console.warn('⚠️ Primary search embedding failed, falling back to browser-based embedding:', error);

    // Fallback to browser-based embedding
    try {
      console.log('🔍 Attempting browser-based search embedding as fallback...');
      return await generateClientSideSearchEmbedding(query);
    } catch (fallbackError) {
      console.error('❌ All search embedding methods failed:', fallbackError);
      throw new Error('Failed to generate search embedding: All methods failed');
    }
  }
}

/**
 * Sync Q&A pair with Qdrant
 */
export async function syncToQdrant(data: {
  question: string;
  answer: string;
  vector: number[];
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Syncing to Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      vectorDimensions: data.vector.length,
    });

    const response = await fetch('/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to sync with Qdrant');
    }

    console.log('✅ Successfully synced to Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync to Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete Q&A pair from Qdrant
 */
export async function deleteFromQdrant(data: {
  question: string;
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting from Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      question: data.question.substring(0, 50) + '...',
    });

    const response = await fetch('/api/qdrant-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      console.warn('⚠️ Qdrant deletion failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to delete from Qdrant'
      };
    }

    console.log('✅ Successfully deleted from Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to delete from Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}



// Primary embedding functions using centralized config
async function generatePrimaryEmbedding(question: string, answer: string = ''): Promise<number[]> {
  try {
    const embeddingConfig = APP_CONFIG.api.embedding;
    console.log(`🌐 Generating ${embeddingConfig.provider} Q&A embedding...`);

    const response = await fetch('/api/generate-openrouter-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question,
        answer,
        type: 'qa_pair'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Check if it's a rate limit or API issue that should trigger fallback
      if (response.status === 429 || response.status === 503 || response.status >= 500) {
        throw new Error(`${embeddingConfig.provider} API temporarily unavailable: ${errorData.error}`);
      } else {
        throw new Error(errorData.error || `${embeddingConfig.provider} API error: ${response.status}`);
      }
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || `${embeddingConfig.provider} embedding generation failed`);
    }

    console.log(`✅ ${embeddingConfig.provider} Q&A embedding generated:`, {
      dimensions: result.embedding.length,
      model: result.model,
      response_time_ms: result.response_time_ms,
      tokens_used: result.tokens_used
    });

    return result.embedding;
  } catch (error) {
    console.error(`❌ ${APP_CONFIG.api.embedding.provider} Q&A embedding generation failed:`, error);
    throw error;
  }
}

async function generatePrimarySearchEmbedding(query: string): Promise<number[]> {
  try {
    const embeddingConfig = APP_CONFIG.api.embedding;
    console.log(`🌐 Generating ${embeddingConfig.provider} search embedding...`);

    const response = await fetch('/api/generate-openrouter-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        type: 'search_query'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Check if it's a rate limit or API issue that should trigger fallback
      if (response.status === 429 || response.status === 503 || response.status >= 500) {
        throw new Error(`${embeddingConfig.provider} API temporarily unavailable: ${errorData.error}`);
      } else {
        throw new Error(errorData.error || `${embeddingConfig.provider} API error: ${response.status}`);
      }
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || `${embeddingConfig.provider} search embedding generation failed`);
    }

    console.log(`✅ ${embeddingConfig.provider} search embedding generated:`, {
      dimensions: result.embedding.length,
      model: result.model,
      response_time_ms: result.response_time_ms,
      tokens_used: result.tokens_used
    });

    return result.embedding;
  } catch (error) {
    console.error(`❌ ${APP_CONFIG.api.embedding.provider} search embedding generation failed:`, error);
    throw error;
  }
}

// Browser-based embedding fallback functions
async function generateClientSideEmbedding(question: string, answer: string): Promise<number[]> {
  try {
    console.log('🧠 Starting client-side embedding generation...');

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Client-side embedding only works in browser environment');
    }

    // Dynamic import to avoid SSR issues
    console.log('📦 Importing @xenova/transformers...');
    const { pipeline } = await import('@xenova/transformers');
    console.log('✅ @xenova/transformers imported successfully');

    console.log('🧠 Initializing embedding model...');
    const fallbackConfig = APP_CONFIG.api.fallbacks.embedding;

    // Initialize embedding pipeline
    const embeddingPipeline = await pipeline(
      'feature-extraction',
      fallbackConfig.model,
      {
        device: 'webgpu', // Falls back to CPU if WebGPU not available
        dtype: 'fp32',
      }
    );

    // Combine question and answer
    const combinedText = answer
      ? `Question: ${question}\nAnswer: ${answer}`
      : `Query: ${question}`;

    console.log('🔄 Processing Q&A pair with embedding model...');

    // Generate embedding
    const result = await embeddingPipeline(combinedText, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];

    console.log('✅ Client-side embedding generated:', {
      dimensions: embedding.length,
      question_length: question.length,
      answer_length: answer.length,
    });

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }

    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return embedding;
  } catch (error) {
    console.error('❌ Client-side embedding generation failed:', error);
    throw new Error(`Client-side embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function generateClientSideSearchEmbedding(query: string): Promise<number[]> {
  try {
    console.log('🔍 Starting client-side search embedding generation...');

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Client-side search embedding only works in browser environment');
    }

    // Dynamic import to avoid SSR issues
    console.log('📦 Importing @xenova/transformers...');
    const { pipeline } = await import('@xenova/transformers');
    console.log('✅ @xenova/transformers imported successfully');

    console.log('🧠 Initializing embedding model...');
    const fallbackConfig = APP_CONFIG.api.fallbacks.embedding;

    // Initialize embedding pipeline
    const embeddingPipeline = await pipeline(
      'feature-extraction',
      fallbackConfig.model,
      {
        device: 'webgpu', // Falls back to CPU if WebGPU not available
        dtype: 'fp32',
      }
    );

    // For search queries, we just use the query text directly
    const searchText = `Query: ${query}`;

    console.log('🔄 Processing search query with embedding model...');

    // Generate embedding
    const result = await embeddingPipeline(searchText, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];

    console.log('✅ Client-side search embedding generated:', {
      dimensions: embedding.length,
      query_length: query.length,
    });

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }

    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return embedding;
  } catch (error) {
    console.error('❌ Client-side search embedding generation failed:', error);
    throw new Error(`Search embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
