-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create collections table
CREATE TABLE IF NOT EXISTS public.collections (
    id TEXT PRIMARY KEY DEFAULT (floor(random() * 9000000000) + 1000000000)::text,
    name TEXT NOT NULL,
    description TEXT,
    user_id TEXT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create qa_pairs table
CREATE TABLE IF NOT EXISTS public.qa_pairs (
    id TEXT PRIMARY KEY DEFAULT (floor(random() * 9000000000) + 1000000000)::text,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    collection_id TEXT NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_collections_user_id ON public.collections(user_id);
CREATE INDEX IF NOT EXISTS idx_qa_pairs_collection_id ON public.qa_pairs(collection_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.qa_pairs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for users table
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid()::text = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid()::text = id);

-- Create RLS policies for collections table
CREATE POLICY "Users can view their own collections" ON public.collections
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own collections" ON public.collections
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own collections" ON public.collections
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own collections" ON public.collections
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create RLS policies for qa_pairs table
CREATE POLICY "Users can view qa_pairs in their collections" ON public.qa_pairs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.collections 
            WHERE collections.id = qa_pairs.collection_id 
            AND collections.user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can insert qa_pairs in their collections" ON public.qa_pairs
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.collections 
            WHERE collections.id = qa_pairs.collection_id 
            AND collections.user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can update qa_pairs in their collections" ON public.qa_pairs
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.collections 
            WHERE collections.id = qa_pairs.collection_id 
            AND collections.user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can delete qa_pairs in their collections" ON public.qa_pairs
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.collections 
            WHERE collections.id = qa_pairs.collection_id 
            AND collections.user_id = auth.uid()::text
        )
    );
