import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { APP_CONFIG } from '@/lib/config';

interface CohereEmbeddingResponse {
  embeddings: number[][];
  texts: string[];
  meta: {
    api_version: {
      version: string;
    };
    billed_units: {
      input_tokens: number;
    };
  };
}

interface CohereErrorResponse {
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    const body = await request.json();
    const { question, answer, query } = body;

    // Validate input - either question or query must be provided
    if (!question && !query) {
      return NextResponse.json(
        { error: 'Either question or query is required' },
        { status: 400 }
      );
    }

    // Check if OpenRouter is configured
    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json(
        { 
          error: 'OpenRouter API key not configured',
          fallback_available: true 
        },
        { status: 503 }
      );
    }

    console.log('🌐 Generating OpenRouter embedding server-side...');

    // Prepare text based on input type
    let text: string;
    let type: string;
    
    if (question) {
      // Q&A embedding
      text = answer 
        ? `Question: ${question}\nAnswer: ${answer}`
        : `Query: ${question}`;
      type = answer ? 'qa-pair' : 'question-only';
    } else {
      // Search query embedding
      text = `Query: ${query}`;
      type = 'search-query';
    }

    const startTime = Date.now();

    // Call Cohere API for embeddings using centralized config
    const embeddingConfig = APP_CONFIG.api.embedding;
    const response = await fetch(embeddingConfig.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${embeddingConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: embeddingConfig.model,
        texts: [text],
        input_type: 'search_document',
        embedding_types: ['float']
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData: CohereErrorResponse = await response.json();

      console.error('❌ Cohere API error:', {
        status: response.status,
        error: errorData.message,
        user_id: user.id
      });

      // Handle specific error types
      let errorMessage = errorData.message;
      if (response.status === 429) {
        errorMessage = `Rate limited: ${errorData.message}`;
      } else if (response.status === 401) {
        errorMessage = 'Invalid Cohere API key';
      } else if (response.status === 402) {
        errorMessage = 'Insufficient credits on Cohere account';
      }

      return NextResponse.json(
        {
          error: errorMessage,
          status_code: response.status,
          fallback_available: true,
          type: 'cohere_api_error'
        },
        { status: response.status }
      );
    }

    const result: CohereEmbeddingResponse = await response.json();
    
    if (!result.embeddings || result.embeddings.length === 0) {
      return NextResponse.json(
        {
          error: 'No embedding data received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    const embedding = result.embeddings[0];

    if (!embedding || embedding.length === 0) {
      return NextResponse.json(
        {
          error: 'Empty embedding received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    // Validate embedding values
    if (embedding.some(val => !isFinite(val))) {
      return NextResponse.json(
        {
          error: 'Invalid embedding values received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    console.log('✅ Cohere embedding generated successfully:', {
      model: embeddingConfig.model,
      dimensions: embedding.length,
      response_time_ms: responseTime,
      tokens_used: result.meta?.billed_units?.input_tokens || 0,
      text_length: text.length,
      type,
      user_id: user.id
    });

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'cohere-api',
      model: embeddingConfig.model,
      text_length: text.length,
      response_time_ms: responseTime,
      tokens_used: result.meta?.billed_units?.input_tokens || 0,
      type,
      features: embeddingConfig.features
    });

  } catch (error) {
    console.error('❌ Cohere embedding generation failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate Cohere embedding',
        details: error instanceof Error ? error.message : 'Unknown error',
        method: 'cohere-api',
        fallback_available: true
      },
      { status: 500 }
    );
  }
}
