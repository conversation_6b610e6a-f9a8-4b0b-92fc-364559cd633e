export const APP_CONFIG = {
  name: 'odude-chat',
  description: 'Multi-user Q&A Collections SaaS',
  theme: {
    primaryColor: 'blue',
    defaultColorScheme: 'dark' as const,
  },
  routes: {
    home: '/',
    dashboard: '/dashboard',
    login: '/auth/signin',
    collections: '/collections',
  },
  // API Provider Configuration - Change providers and models from here
  api: {
    // Embedding Provider Configuration
    embedding: {
      provider: 'cohere', // 'cohere' | 'openai' | 'openrouter'
      model: 'embed-multilingual-v3.0',
      apiKey: process.env.COHERE_API_KEY,
      endpoint: 'https://api.cohere.ai/v1/embed',
      features: ['Multilingual support', 'Hindi-English optimized', 'Nepali-English optimized']
    },
    // Chat Provider Configuration
    chat: {
      provider: 'gemini', // 'gemini' | 'openai' | 'openrouter'
      model: 'gemini-1.5-flash',
      apiKey: process.env.GEMINI_API_KEY,
      maxTokens: 1000,
      temperature: 0.7,
    },
    // Fallback configurations
    fallbacks: {
      embedding: {
        provider: 'transformers', // Browser-based fallback
        model: 'Xenova/all-MiniLM-L6-v2'
      }
    }
  },
  rag: {
    // Supported language tones for RAG chatbot
    supportedLanguages: [
      { code: 'hi-en', name: 'Hindi-English', description: 'Mixed Hindi and English responses' },
      { code: 'ne-en', name: 'Nepali-English', description: 'Mixed Nepali and English responses' },
      { code: 'en', name: 'English', description: 'Pure English responses' },
    ],
    // Default settings
    defaultLanguage: 'en',
    maxSearchResults: 5,
    similarityThreshold: 0.7,
  },
} as const;